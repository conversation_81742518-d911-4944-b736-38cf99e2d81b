#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
Predicts 2-day ahead stock price direction using 'Adj Close' and 'Volume' features
Uses 3.5-year training windows for consistency with fine-tuning service
"""

import os
import sys
import json
import logging
from datetime import date, timedelta
from pathlib import Path

# Module-level constants - will be calculated dynamically for 3.5-year training window
PRED_DATES = [date(2025, 6, 4), date(2025, 6, 5)]

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
from sklearn.calibration import CalibratedClassifierCV
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import roc_curve
import joblib
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.regularizers import l2
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import AdamW

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def load_and_prepare_data(ticker, use_volume=False):
    """Load data and prepare for training with 3.5-year training window"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]              # …/financial_dashboard
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)

        # Parse Date column
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (3.5 years before first prediction date)
        train_end_date = PRED_DATES[0] - timedelta(days=1)  # Day before first prediction
        train_start_date = PRED_DATES[0] - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if not volume_path.exists():
                print(f"Volume data file not found: {volume_path}", file=sys.stderr)
                sys.exit(1)

            volume_df = pd.read_csv(volume_path)
            volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

            # Convert numeric columns to float64
            for col in volume_df.columns:
                if col != 'Date':
                    volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

            if ticker not in volume_df.columns:
                print(f"Ticker {ticker} not found in volume data", file=sys.stderr)
                sys.exit(1)

            volume_data = volume_df[['Date', ticker]].copy()
            volume_data.columns = ['Date', 'Volume']

        # Filter training data using 3.5-year window
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()

        # Check minimum rows requirement
        if len(train_data) < 100:  # Increased minimum for 3.5-year window
            print(f"Insufficient training data: {len(train_data)} rows (minimum 100 required)", file=sys.stderr)
            sys.exit(1)

        # Get all data for predictions
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) &
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(data, sequence_length=60, use_volume=False, prediction_horizon=2):
    """Create sequences for LSTM training with configurable prediction horizon"""
    X, y = [], []

    if use_volume:
        # data should be 2D array with [price, volume] columns
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target based on price (first column): 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1, 0]  # Current day price
            future_price = data[i + prediction_horizon - 1, 0]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)
    else:
        # data is 1D array with price only
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target: 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1]  # Current day price
            future_price = data[i + prediction_horizon - 1]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape):
    """Build Intel-optimized LSTM classifier model with L2 regularization"""
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.1),

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def get_calibrated_probability(model, calibrator, sequence):
    """Get calibrated probability using trained calibrator (Platt scaling or isotonic)"""
    raw_prediction = model.predict(sequence, verbose=0)[0][0]
    if hasattr(calibrator, 'predict_proba'):
        # Platt scaling (LogisticRegression)
        calibrated_prob = calibrator.predict_proba([[raw_prediction]])[0, 1]
    else:
        # Fallback to raw prediction
        calibrated_prob = raw_prediction
    return calibrated_prob


def predict_for_dates(model, scaler, all_data, pred_dates, use_volume=False, prediction_horizon=2, calibrator=None, best_threshold=0.5):
    """Make predictions for specific dates with 2-day horizon"""
    predictions = []
    seen_predictions = set()  # Prevent duplicate predictions

    for pred_date in pred_dates:
        # Find the date in data
        date_mask = all_data['Date'] == pred_date
        if not date_mask.any():
            print(f"Date {pred_date} not found in data", file=sys.stderr)
            sys.exit(1)

        date_idx = all_data[date_mask].index[0]

        # Get 60 days before this date for prediction
        if date_idx < 60:
            print(f"Insufficient data before {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Check if we have enough future data for actual label calculation
        if date_idx + prediction_horizon - 1 >= len(all_data):
            print(f"Insufficient future data for {prediction_horizon}-day prediction at {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Get sequence data
        if use_volume:
            # Get both price and volume data
            sequence_data = all_data.iloc[date_idx-60:date_idx][['Adj Close', 'Volume']].values
            sequence_scaled = scaler.transform(sequence_data)
            sequence_scaled = sequence_scaled.reshape(1, 60, 2)
        else:
            # Get price data only
            sequence_data = all_data.iloc[date_idx-60:date_idx]['Adj Close'].values
            sequence_scaled = scaler.transform(sequence_data.reshape(-1, 1))
            sequence_scaled = sequence_scaled.reshape(1, 60, 1)

        # Make prediction with calibration if available
        if calibrator is not None:
            pred_prob_up = get_calibrated_probability(model, calibrator, sequence_scaled)
            pred_prob_down = 1.0 - pred_prob_up
        else:
            pred_prob_up = model.predict(sequence_scaled, verbose=0)[0][0]
            pred_prob_down = 1.0 - pred_prob_up

        # Use optimal threshold for binary classification
        predicted_label = int(pred_prob_up > best_threshold)

        # Get actual value for 2-day horizon
        current_price = all_data.iloc[date_idx-1]['Adj Close']  # Price at prediction date
        future_price = all_data.iloc[date_idx + prediction_horizon - 1]['Adj Close']  # Price 2 days later
        actual_label = 1 if future_price > current_price else 0

        # Prevent duplicate predictions
        key = (pred_date, prediction_horizon)
        if key not in seen_predictions:
            predictions.append({
                "date": pred_date.strftime("%Y-%m-%d"),
                "pred_prob_up": round(float(pred_prob_up), 4),
                "pred_prob_down": round(float(pred_prob_down), 4),
                "predicted_label": int(predicted_label),
                "actual_label": int(actual_label),
                "prediction_horizon": prediction_horizon,
                "optimal_threshold": round(float(best_threshold), 4)
            })
            seen_predictions.add(key)

    return predictions


def determine_result_color(predictions):
    """Determine result color based on prediction accuracy"""
    correct_count = sum(1 for p in predictions if p["predicted_label"] == p["actual_label"])

    if correct_count == 2:
        return "green"
    elif correct_count == 1:
        return "yellow"
    else:
        return "red"


def main():
    """Main function"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--no-volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    # Enable volume by default, disable with --no-volume flag
    use_volume = True
    if len(sys.argv) == 3 and sys.argv[2] == '--no-volume':
        use_volume = False

    try:
        # Load and prepare data with 3.5-year training window
        all_data, train_data, train_end_date = load_and_prepare_data(ticker, use_volume)

        # Prepare training data
        if use_volume:
            # Use both price and volume data
            train_features = train_data[['Adj Close', 'Volume']].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_features)

            # Create sequences with volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=True, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 2)

            # Build model for 2 features (price + volume)
            model = build_lstm_model((60, 2))
        else:
            # Use price data only (backward compatibility)
            train_prices = train_data['Adj Close'].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_prices.reshape(-1, 1)).flatten()

            # Create sequences without volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=False, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 1)

            # Build model for 1 feature (price only)
            model = build_lstm_model((60, 1))

        # Manual time series validation split (preserve order)
        split_idx = int(len(X_train) * 0.9)
        X_tr, X_val = X_train[:split_idx], X_train[split_idx:]
        y_tr, y_val = y_train[:split_idx], y_train[split_idx:]

        # Calculate class weights to address imbalance
        class_weights = compute_class_weight('balanced', classes=np.array([0, 1]), y=y_tr)
        class_weight_dict = {0: class_weights[0], 1: class_weights[1]}

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=2,
            restore_best_weights=True
        )

        # Train with class weights and manual validation split
        model.fit(
            X_tr, y_tr,
            epochs=8,
            batch_size=256,
            validation_data=(X_val, y_val),
            callbacks=[early_stopping],
            class_weight=class_weight_dict,
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Robust probability calibration with automatic method selection
        raw_val_predictions = model.predict(X_val, verbose=0).ravel()

        calibrator = None
        best_threshold = 0.5  # Default threshold

        # Check data characteristics for calibration method selection
        pred_variance = np.var(raw_val_predictions)
        unique_preds = len(np.unique(raw_val_predictions))
        val_size = len(raw_val_predictions)

        print(f"[DEBUG] Validation data - Size: {val_size}, Variance: {pred_variance:.6f}, Unique values: {unique_preds}", file=sys.stderr)

        # Use Platt scaling for small datasets (<1000 samples) or low variance data
        # Research shows Platt scaling is more robust for small datasets
        if val_size >= 20 and pred_variance > 1e-6 and unique_preds >= 5:
            try:
                # Use Platt scaling (LogisticRegression) - more robust for small datasets
                calibrator = LogisticRegression(max_iter=1000)
                calibrator.fit(raw_val_predictions.reshape(-1, 1), y_val)

                # Get calibrated validation predictions for threshold calculation
                calibrated_val_preds = calibrator.predict_proba(raw_val_predictions.reshape(-1, 1))[:, 1]

                # Verify calibration actually changed predictions
                calibration_variance = np.var(calibrated_val_preds)
                if calibration_variance > 1e-6:
                    # Calculate optimal threshold using calibrated scores
                    fpr, tpr, thresholds = roc_curve(y_val, calibrated_val_preds)
                    best_threshold = thresholds[(tpr - fpr).argmax()]
                    print(f"[DEBUG] Platt scaling successful - Calibrated variance: {calibration_variance:.6f}, Optimal threshold: {best_threshold:.4f}", file=sys.stderr)
                else:
                    print(f"[DEBUG] Platt scaling failed (flat output), using default threshold 0.5", file=sys.stderr)
                    calibrator = None

            except Exception as e:
                print(f"[DEBUG] Platt scaling error: {e}, using default threshold 0.5", file=sys.stderr)
                calibrator = None
        else:
            print(f"[DEBUG] Insufficient data for calibration (size: {val_size}, variance: {pred_variance:.6f}), using default threshold 0.5", file=sys.stderr)

        # Save model and calibrators
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        # Save all components
        model.save(model_dir / f"{ticker}_model.h5")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler.pkl")
        joblib.dump(calibrator, model_dir / f"{ticker}_calibrator.pkl")
        joblib.dump(best_threshold, model_dir / f"{ticker}_threshold.pkl")

        # Make predictions for target dates with 2-day horizon
        predictions = predict_for_dates(model, scaler, all_data, PRED_DATES, use_volume, prediction_horizon=2, calibrator=calibrator, best_threshold=best_threshold)

        # Determine result color
        result_color = determine_result_color(predictions)

        # Add result color to each prediction
        for pred in predictions:
            pred["result_color"] = result_color

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_end_date.strftime("%Y-%m-%d"),
            "predictions": predictions
        }



        # Print JSON result to stdout
        print(json.dumps(result))

        # Log completion
        logging.info(f"[{ticker}] LSTM completed – result: {result_color}")

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()