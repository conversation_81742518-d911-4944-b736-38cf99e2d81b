#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
Flexible date selection with 3.5-year training windows
Uses 'Adj Close' and 'Volume' features with 2-day prediction horizon
"""

import os
import sys
import json
import logging
import random
import re
import argparse
from datetime import date, timedelta, datetime
from pathlib import Path

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
try:
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')
    
    # Enable Intel optimizations
    tf.config.threading.set_intra_op_parallelism_threads(16)
    tf.config.threading.set_inter_op_parallelism_threads(16)
    
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import AdamW
    from tensorflow.keras.regularizers import l2
    from tensorflow.keras.callbacks import EarlyStopping
    
    print("✅  TensorFlow-Intel 2.18.0 — oneDNN enabled", file=sys.stderr)
    
except ImportError as e:
    print(f"TensorFlow import error: {e}", file=sys.stderr)
    sys.exit(1)

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
from sklearn.calibration import CalibratedClassifierCV
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import roc_curve
import joblib


def get_random_target_date():
    """Generate random target date between 2024-09-01 and 2025-05-15"""
    start_date = date(2024, 9, 1)
    end_date = date(2025, 5, 15)

    # Calculate the number of days between start and end
    days_between = (end_date - start_date).days

    # Generate random number of days to add to start date
    random_days = random.randint(0, days_between)

    # Return the random date
    return start_date + timedelta(days=random_days)


def load_sp500_tickers():
    """Load S&P 500 tickers from sp500_enriched_final.ts"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    ts_file = ROOT_DIR / "data" / "sp500_enriched_final.ts"

    if not ts_file.exists():
        print(f"Error: {ts_file} not found", file=sys.stderr)
        sys.exit(1)

    tickers = []
    company_names = {}

    try:
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract ticker symbols and company names using regex
        pattern = r'"([A-Z]+)":\s*{\s*name:\s*"([^"]+)"'
        matches = re.findall(pattern, content)

        for ticker, name in matches:
            tickers.append(ticker)
            company_names[ticker] = name

        return tickers, company_names

    except Exception as e:
        print(f"Error loading tickers: {e}", file=sys.stderr)
        sys.exit(1)


def get_available_dates():
    """Load available dates from the CSV file"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

    if not csv_path.exists():
        print(f"Error: {csv_path} not found", file=sys.stderr)
        sys.exit(1)

    try:
        import pandas as pd
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Filter dates between September 2024 and May 15, 2025
        start_date = date(2024, 9, 1)
        end_date = date(2025, 5, 15)

        available_dates = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]['Date'].tolist()
        return available_dates

    except Exception as e:
        print(f"Error loading available dates: {e}", file=sys.stderr)
        sys.exit(1)


def get_random_date_and_ticker(count=1):
    """Generate random dates and tickers for fine-tuning"""
    # Load all available tickers
    all_tickers, company_names = load_sp500_tickers()

    # Exclude specified tickers
    excluded_tickers = {'SW', 'GEV', 'SOLV', 'VLTO', 'KVUE', 'GEHC', 'CEG'}
    available_tickers = [t for t in all_tickers if t not in excluded_tickers]

    # Load available dates from the dataset
    available_dates = get_available_dates()

    if not available_dates:
        print("No available dates found in the specified range", file=sys.stderr)
        sys.exit(1)

    # Generate random selections
    selections = []
    for _ in range(count):
        # Random date from available dates
        random_date = random.choice(available_dates)

        # Random ticker
        random_ticker = random.choice(available_tickers)

        selections.append({
            'ticker': random_ticker,
            'company_name': company_names[random_ticker],
            'date': random_date
        })

    return selections


def load_and_prepare_data(ticker, target_date, use_volume=False):
    """Load data and prepare for training with flexible date selection"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (2 days before target date to match lstm_service.py logic)
        train_end_date = target_date - timedelta(days=2)  # 2 days before target
        train_start_date = target_date - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Filter training data
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()
        
        if len(train_data) < 100:  # Minimum data requirement
            print(f"Insufficient training data for {ticker} at {target_date}", file=sys.stderr)
            sys.exit(1)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if volume_path.exists():
                volume_df = pd.read_csv(volume_path)
                volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

                # Convert numeric columns to float64
                for col in volume_df.columns:
                    if col != 'Date':
                        volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

                if ticker in volume_df.columns:
                    volume_data = volume_df[['Date', ticker]].copy()
                    volume_data.columns = ['Date', 'Volume']

        # Get all data for predictions (including target date)
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) & 
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(data, sequence_length=60, use_volume=False, prediction_horizon=2):
    """Create sequences for LSTM training with configurable prediction horizon"""
    X, y = [], []

    if use_volume:
        # data should be 2D array with [price, volume] columns
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target based on price (first column): 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1, 0]  # Current day price
            future_price = data[i + prediction_horizon - 1, 0]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)
    else:
        # data is 1D array with price only
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target: 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1]  # Current day price
            future_price = data[i + prediction_horizon - 1]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape):
    """Build Intel-optimized LSTM classifier model with L2 regularization"""
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.1),

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def get_calibrated_probability(model, calibrator, sequence):
    """Get calibrated probability using trained calibrator (Platt scaling or isotonic)"""
    raw_prediction = model.predict(sequence, verbose=0)[0][0]
    if hasattr(calibrator, 'predict_proba'):
        # Platt scaling (LogisticRegression)
        calibrated_prob = calibrator.predict_proba([[raw_prediction]])[0, 1]
    else:
        # Fallback to raw prediction
        calibrated_prob = raw_prediction
    return calibrated_prob


def predict_for_dates(model, scaler, all_data, target_date, use_volume=False, prediction_horizon=2, calibrator=None, best_threshold=0.5):
    """Make predictions for both 1 day before target date AND target date itself (matching lstm_service.py)"""
    # Calculate the two prediction dates: 1 day before target and target date itself
    pred_date_1 = target_date - timedelta(days=1)  # 1 day before target
    pred_date_2 = target_date  # target date itself
    pred_dates = [pred_date_1, pred_date_2]

    predictions = []
    seen_predictions = set()  # Prevent duplicate predictions

    for pred_date in pred_dates:
        # Find the exact date or nearest available date
        date_mask = all_data['Date'] == pred_date

        if not date_mask.any():
            # If exact date not found, find the nearest available date
            print(f"Exact date {pred_date} not found, finding nearest available date", file=sys.stderr)

            # Find the closest date before the prediction date
            available_dates = all_data['Date'].values
            dates_before = [d for d in available_dates if d <= pred_date]

            if not dates_before:
                print(f"No data available before {pred_date}", file=sys.stderr)
                sys.exit(1)

            # Use the most recent date before prediction date
            actual_date = max(dates_before)
            date_mask = all_data['Date'] == actual_date
            print(f"Using nearest available date: {actual_date}", file=sys.stderr)
        else:
            actual_date = pred_date

        date_idx = all_data[date_mask].index[0]

        # Get 60 days before this date for prediction
        if date_idx < 60:
            print(f"Insufficient data before {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Check if we have enough future data for actual label calculation
        if date_idx + prediction_horizon - 1 >= len(all_data):
            print(f"Insufficient future data for {prediction_horizon}-day prediction at {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Get sequence data
        if use_volume:
            # Get both price and volume data
            sequence_data = all_data.iloc[date_idx-60:date_idx][['Adj Close', 'Volume']].values
            sequence_scaled = scaler.transform(sequence_data)
            sequence_scaled = sequence_scaled.reshape(1, 60, 2)
        else:
            # Get price data only
            sequence_data = all_data.iloc[date_idx-60:date_idx]['Adj Close'].values
            sequence_scaled = scaler.transform(sequence_data.reshape(-1, 1))
            sequence_scaled = sequence_scaled.reshape(1, 60, 1)

        # Make prediction with calibration if available
        if calibrator is not None:
            pred_prob_up = get_calibrated_probability(model, calibrator, sequence_scaled)
            pred_prob_down = 1.0 - pred_prob_up
        else:
            pred_prob_up = model.predict(sequence_scaled, verbose=0)[0][0]
            pred_prob_down = 1.0 - pred_prob_up

        # Use optimal threshold for binary classification
        predicted_label = int(pred_prob_up > best_threshold)

        # Get actual value for 2-day horizon
        current_price = all_data.iloc[date_idx-1]['Adj Close']  # Price at prediction date
        future_price = all_data.iloc[date_idx + prediction_horizon - 1]['Adj Close']  # Price 2 days later
        actual_label = 1 if future_price > current_price else 0

        # Prevent duplicate predictions
        key = (actual_date, prediction_horizon)
        if key not in seen_predictions:
            predictions.append({
                "date": actual_date.strftime("%Y-%m-%d"),
                "pred_prob_up": round(float(pred_prob_up), 4),
                "pred_prob_down": round(float(pred_prob_down), 4),
                "predicted_label": int(predicted_label),
                "actual_label": int(actual_label),
                "prediction_horizon": prediction_horizon,
                "optimal_threshold": round(float(best_threshold), 4)
            })
            seen_predictions.add(key)

    return predictions


def determine_result_color(predictions):
    """Determine result color based on prediction accuracy (matching lstm_service.py)"""
    correct_count = sum(1 for p in predictions if p["predicted_label"] == p["actual_label"])

    if correct_count == 2:
        return "green"
    elif correct_count == 1:
        return "yellow"
    else:
        return "red"


def save_result_to_file(result, company_name=None, target_date=None, output_dir=None):
    """Save LSTM result to JSON file with proper naming convention"""
    # Set default output directory
    if output_dir is None:
        ROOT_DIR = Path(__file__).resolve().parents[1]
        output_dir = ROOT_DIR / "data" / "finetuning"

    # Ensure output directory exists
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create filename based on company name and date
    if company_name and target_date:
        # Clean company name for filename
        clean_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        clean_name = re.sub(r'[-\s]+', '_', clean_name)
        if isinstance(target_date, date):
            target_date_str = target_date.strftime('%Y-%m-%d')
        else:
            target_date_str = str(target_date)
        filename = f"{clean_name}_{target_date_str}.json"
    else:
        # Fallback to timestamp-based naming
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lstm_result_{ts}.json"

    # Save to file
    file_path = output_dir / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    return file_path


def process_single_ticker(ticker, target_date, use_volume=True, save_to_file=False, company_name=None):
    """Process a single ticker and return the result"""
    try:
        # Load and prepare data
        all_data, train_data, train_end_date = load_and_prepare_data(ticker, target_date, use_volume)

        # Prepare training data
        if use_volume:
            # Use both price and volume data
            train_features = train_data[['Adj Close', 'Volume']].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_features)

            # Create sequences with volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=True, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 2)

            # Build model for 2 features (price + volume)
            model = build_lstm_model((60, 2))
        else:
            # Use price data only
            train_prices = train_data['Adj Close'].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_prices.reshape(-1, 1)).flatten()

            # Create sequences without volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=False, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 1)

            # Build model for 1 feature (price only)
            model = build_lstm_model((60, 1))

        # Manual time series validation split (preserve order)
        split_idx = int(len(X_train) * 0.9)
        X_tr, X_val = X_train[:split_idx], X_train[split_idx:]
        y_tr, y_val = y_train[:split_idx], y_train[split_idx:]

        # Calculate class weights to address imbalance
        class_weights = compute_class_weight('balanced', classes=np.array([0, 1]), y=y_tr)
        class_weight_dict = {0: class_weights[0], 1: class_weights[1]}

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=3,
            restore_best_weights=True
        )

        # Train with class weights and manual validation split
        model.fit(
            X_tr, y_tr,
            epochs=10,
            batch_size=256,
            validation_data=(X_val, y_val),
            callbacks=[early_stopping],
            class_weight=class_weight_dict,
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Robust probability calibration with automatic method selection
        raw_val_predictions = model.predict(X_val, verbose=0).ravel()

        calibrator = None
        best_threshold = 0.5  # Default threshold

        # Check data characteristics for calibration method selection
        pred_variance = np.var(raw_val_predictions)
        unique_preds = len(np.unique(raw_val_predictions))
        val_size = len(raw_val_predictions)

        print(f"[DEBUG] Validation data - Size: {val_size}, Variance: {pred_variance:.6f}, Unique values: {unique_preds}", file=sys.stderr)

        # Use Platt scaling for small datasets (<1000 samples) or low variance data
        # Research shows Platt scaling is more robust for small datasets
        if val_size >= 20 and pred_variance > 1e-6 and unique_preds >= 5:
            try:
                # Use Platt scaling (LogisticRegression) - more robust for small datasets
                calibrator = LogisticRegression(max_iter=1000)
                calibrator.fit(raw_val_predictions.reshape(-1, 1), y_val)

                # Get calibrated validation predictions for threshold calculation
                calibrated_val_preds = calibrator.predict_proba(raw_val_predictions.reshape(-1, 1))[:, 1]

                # Verify calibration actually changed predictions
                calibration_variance = np.var(calibrated_val_preds)
                if calibration_variance > 1e-6:
                    # Calculate optimal threshold using calibrated scores
                    fpr, tpr, thresholds = roc_curve(y_val, calibrated_val_preds)
                    best_threshold = thresholds[(tpr - fpr).argmax()]
                    print(f"[DEBUG] Platt scaling successful - Calibrated variance: {calibration_variance:.6f}, Optimal threshold: {best_threshold:.4f}", file=sys.stderr)
                else:
                    print(f"[DEBUG] Platt scaling failed (flat output), using default threshold 0.5", file=sys.stderr)
                    calibrator = None

            except Exception as e:
                print(f"[DEBUG] Platt scaling error: {e}, using default threshold 0.5", file=sys.stderr)
                calibrator = None
        else:
            print(f"[DEBUG] Insufficient data for calibration (size: {val_size}, variance: {pred_variance:.6f}), using default threshold 0.5", file=sys.stderr)

        # Save model and calibrators
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        # Save all components
        model.save(model_dir / f"{ticker}_model.h5")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler.pkl")
        joblib.dump(calibrator, model_dir / f"{ticker}_calibrator.pkl")
        joblib.dump(best_threshold, model_dir / f"{ticker}_threshold.pkl")

        # Make predictions for both 1 day before target date AND target date itself
        predictions = predict_for_dates(model, scaler, all_data, target_date, use_volume, prediction_horizon=2, calibrator=calibrator, best_threshold=best_threshold)

        # Determine result color based on 2 predictions
        result_color = determine_result_color(predictions)

        # Add result color to each prediction
        for pred in predictions:
            pred["result_color"] = result_color

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_end_date.strftime("%Y-%m-%d"),
            "target_date": target_date.strftime("%Y-%m-%d"),
            "predictions": predictions
        }

        # Save to file if requested
        if save_to_file:
            file_path = save_result_to_file(result, company_name, target_date)
            print(f"✔ {ticker} → {file_path.name} saved", file=sys.stderr)

        return result

    except Exception as e:
        print(f"Error processing {ticker}: {e}", file=sys.stderr)
        return None


def main():
    """Main function for LSTM fine-tuning service with batch processing support"""
    parser = argparse.ArgumentParser(
        description="LSTM Fine-tuning Service with Random Ticker and Date Selection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python lstm_finetuning.py --count 10           # Generate 10 random samples
  python lstm_finetuning.py AAPL 2024-12-15      # Specific ticker and date
  python lstm_finetuning.py AAPL --random        # Specific ticker, random date
  python lstm_finetuning.py AAPL                 # Specific ticker, random date
        """
    )

    parser.add_argument(
        '--count', '-c',
        type=int,
        help='Number of random fine-tuning samples to generate'
    )

    parser.add_argument(
        'ticker',
        nargs='?',
        help='Ticker symbol (required unless using --count)'
    )

    parser.add_argument(
        'date',
        nargs='?',
        help='Target date (YYYY-MM-DD) or --random'
    )

    parser.add_argument(
        '--no-volume',
        action='store_true',
        help='Disable volume features (use price only)'
    )

    args = parser.parse_args()

    # Enable volume by default, disable if --no-volume flag is used
    use_volume = not args.no_volume

    if args.count:
        # Batch processing mode: generate multiple random samples
        print(f"[INFO] Generating {args.count} random LSTM fine-tuning samples...", file=sys.stderr)
        selections = get_random_date_and_ticker(args.count)

        success_count = 0
        for i, selection in enumerate(selections, 1):
            try:
                ticker = selection['ticker']
                company_name = selection['company_name']
                target_date = selection['date']

                print(f"[INFO] Processing {i}/{args.count}: {ticker} ({company_name}) on {target_date.strftime('%Y-%m-%d')}", file=sys.stderr)

                result = process_single_ticker(ticker, target_date, use_volume, save_to_file=True, company_name=company_name)
                if result:
                    success_count += 1

            except Exception as e:
                print(f"[ERROR] {selection['ticker']}: {e}", file=sys.stderr)

        print(f"[INFO] Completed! Successfully processed {success_count}/{args.count} samples", file=sys.stderr)

    else:
        # Single ticker mode
        if not args.ticker:
            parser.error("Ticker is required unless using --count mode")

        ticker = args.ticker.upper()

        # Determine target date
        if args.date:
            if args.date == '--random':
                target_date = get_random_target_date()
                print(f"Using random target date: {target_date}", file=sys.stderr)
            else:
                try:
                    target_date = date.fromisoformat(args.date)
                except ValueError:
                    print(f"Invalid date format: {args.date}. Use YYYY-MM-DD or --random", file=sys.stderr)
                    sys.exit(1)
        else:
            # Default to random date if no date specified
            target_date = get_random_target_date()
            print(f"Using random target date: {target_date}", file=sys.stderr)

        # Process single ticker and output to stdout
        result = process_single_ticker(ticker, target_date, use_volume, save_to_file=False)
        if result:
            print(json.dumps(result))
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
